@font-face {
  font-family: '思源宋体';
  src: url(https://ztimg.hefei.cc/static/common/fonts/思源宋体.otf);
}
@font-face {
  font-family: 'HANCHANSHUTI';
  src: url(../HANCHANSHUTI·LONGCANG.OTF);
}
body,
div,
h1,
h2,
h3,
h4,
h5,
h6,
html,
li,
p,
span {
  font-size: 3.5vw;
}
.musicbtn {
  width: 9.6vw;
  height: 9.6vw;
  top: 3.6vw;
  right: 2.8vw;
  background-image: url(../img/music.png);
  z-index: 11;
}
.logo {
  width: 13.6vw;
  position: absolute;
  top: 3.6vw;
  left: 2.8vw;
  z-index: 11;
}
.warp {
  margin: 0 auto;
  min-height: 170vw;
  height: 100vh;
  width: 100vw;
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: '思源宋体';
}
.warp .page {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
  color: #fff;
  background: url(../img/bj.jpg?v=1) no-repeat center center / 100% 100%;
}
.warp .page .title {
  margin-top: -10vw;
  width: 61.7333vw;
  z-index: 2;
}
.warp .page .title2 {
  margin-top: 10vw;
  width: 71.0667vw;
}
.warp .page .title3 {
  width: 63.2vw;
  position: absolute;
  top: 13vh;
}
.warp .page .main {
  margin-top: 4vw;
  width: 86.5333vw;
}
.warp .page .start {
  margin-top: 2vw;
  width: 59.6vw;
  height: 14.5333vw;
  flex-shrink: 0;
  background: url(../img/start.png) no-repeat center center / 100% 100%;
  z-index: 2;
}
.warp .page .step {
  width: 100%;
}
.warp .page .box_area {
  margin-top: 6vw;
  width: 78.6667vw;
  height: 96vw;
  position: relative;
  overflow: hidden;
}
.warp .page .box_area .box {
  width: 100%;
  position: relative;
  pointer-events: none;
}
.warp .page .poster_area {
  position: relative;
}
.warp .page .poster_area .poster {
  margin-top: 6vw;
  width: 78.6667vw;
}
.warp .page .poster_area .cheering_message {
  width: 37.867vw;
  position: absolute;
  bottom: 16vw;
  right: 2vw;
}
.warp .page .tip {
  margin-top: 2vw;
  color: #fff;
  font-weight: bold;
  font-size: 4vw;
  letter-spacing: 0.4vw;
  transition: 0.6s;
}
.warp .page .bg2 {
  width: 100vw;
  position: absolute;
  left: 0;
  bottom: 0;
  pointer-events: none;
}
.warp .page .button_container {
  position: absolute;
  z-index: 2;
  bottom: 7vw;
  display: flex;
  justify-content: center;
}
.warp .page .button_container .button {
  height: 10vw;
  margin: 0 6vw;
}
.warp .page .button_container .gray {
  filter: grayscale(100%);
}
.warp .page .rule {
  margin-top: 6vw;
  width: 100vw;
}
.warp .page .back {
  margin-top: 9.4667vw;
  height: 11.7333vw;
}
.warp .page .nav {
  margin-top: 7.3333vw;
  width: 84.933vw;
  height: 7.2vw;
  background: #00A3F0;
  border-radius: 1.2vw;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.warp .page .nav .scan {
  width: 25.7333vw;
  margin-left: 1.3333vw;
}
.warp .page .nav .flag_num {
  margin-top: -1vw;
  font-size: 3vw;
  display: flex;
  align-items: center;
}
.warp .page .nav .flag_num span {
  color: #FF9C00;
  font-size: 4.8vw;
  font-weight: bold;
  margin: 0 1vw;
}
.warp .page .nav .tab_area {
  width: 47.067vw;
  height: 7.2vw;
  background: #FFFFF9;
  border-radius: 1.2vw;
  padding: 0 3vw;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.warp .page .nav .tab_area .tab_area_container {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}
.warp .page .nav .tab_area .tab_area_container .slide {
  position: absolute;
  height: 5.867vw;
  width: 25%;
  background: #FB9B00;
  border-radius: 0.8vw;
  left: 0;
  transition: 0.3s;
}
.warp .page .nav .tab_area .tab_area_container .tab_item {
  flex: 1;
  color: #C9C9C9;
  height: 5.867vw;
  text-align: center;
  font-weight: bold;
  position: relative;
  z-index: 1;
  transition: 0.3s;
}
.warp .page .nav .tab_area .tab_area_container .active {
  color: #fff;
}
.warp .page .flag_area {
  margin-top: 4vw;
  width: 77.467vw;
  height: 34.133vw;
  background: rgba(255, 255, 255, 0.42);
  border-radius: 2vw;
  display: flex;
  align-items: center;
  justify-content: center;
}
.warp .page .flag_area .flag_img {
  width: 5.4667vw;
}
.warp .page .flag_area .right {
  margin-left: 2vw;
  width: 66vw;
  height: 28.267vw;
  background: rgba(66, 133, 199, 0.75);
  border-radius: 2vw;
  display: flex;
  align-items: center;
  justify-content: space-around;
  flex-wrap: wrap;
}
.warp .page .flag_area .right .flag_text {
  height: 33.3%;
  width: 50%;
  padding-left: 2vw;
  font-size: 3vw;
  display: flex;
  align-items: center;
}
.warp .page .flag_list {
  margin-top: 9vw;
  font-family: 'HANCHANSHUTI';
  width: 100%;
}
.warp .page .flag_list .flag_row {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 3vw 0;
  position: relative;
}
.warp .page .flag_list .flag_row .flag {
  color: #fff;
  font-size: 4.5vw;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  position: relative;
}
.warp .page .flag_list .flag_row .flag_separator {
  color: #006CBD;
  font-size: 5.2vw;
  pointer-events: none;
  margin-right: 2vw;
}
.warp .page .input_area {
  margin-top: 14vw;
  width: 84.933vw;
  height: 7.2vw;
  background: #006CBD;
  border-radius: 1.2vw;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 2vw;
}
.warp .page .input_area .add0 {
  width: 11.4667vw;
}
.warp .page .input_area input {
  font-family: 'HANCHANSHUTI';
  width: 56.933vw;
  height: 7.2vw;
  background: #FFFFF9;
  border-radius: 1.2vw;
  text-align: center;
}
.warp .page .input_area input::-webkit-input-placeholder {
  color: #ADADAD;
}
.warp .page .input_area input:-moz-placeholder {
  color: #ADADAD;
}
.warp .page .input_area input::-moz-placeholder {
  color: #ADADAD;
}
.warp .page .input_area input:-ms-input-placeholder {
  color: #ADADAD;
}
.warp .page .input_area .add {
  width: 8.2667vw;
}
.warp .page .button13 {
  margin-top: 14.8vw;
  width: 46.2667vw;
}
.warp .page .cheering_message_area {
  margin-top: 10vw;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
}
.warp .page .cheering_message_area .cheering_message {
  width: 42.667vw;
  height: 30.133vw;
  background: rgba(255, 255, 255, 0.32);
  border-radius: 2.4vw;
  margin: 1vw;
  padding: 2vw;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}
.warp .page .cheering_message_area .cheering_message .index {
  position: absolute;
  top: 1vw;
  left: 3vw;
  font-size: 7.067vw;
  font-family: 'HANCHANSHUTI';
  color: #FFFFFF;
}
.warp .page .cheering_message_area .cheering_message .cheering_message_img {
  width: 34.1333vw;
}
.warp .page .cheering_message_area .active .cheering_message_img {
  animation: pulsate-bck2 1s ease-in-out infinite both;
}
.warp .page .button_area {
  margin-top: 4vw;
  display: flex;
  align-items: center;
}
.warp .page .button_area .button {
  height: 16vw;
  margin: 0 1vw;
}
.blur {
  filter: blur(1vw);
}
.fc {
  justify-content: center;
}
.mask {
  z-index: 10;
  position: fixed;
  top: 0;
  left: 0;
  min-height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: rgba(18, 45, 29, 0.3);
  transform: translateX(-50%);
  left: 50%;
  color: #fff;
}
.mask .popup {
  margin-top: -1vw;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}
.mask .popup .back {
  width: 49.7333vw;
  position: absolute;
  bottom: -6vw;
}
.mask .popup1 {
  width: 83.4667vw;
  height: 48.4vw;
  background: url(../img/popup1.png) no-repeat center top / 100% 100%;
}
.mask .popup2 {
  margin-top: -20vw;
  width: 70.4vw;
  height: 118.4vw;
  background: url(../img/popup2.png) no-repeat center top / 100% 100%;
}
.mask .popup2 .p1 {
  position: absolute;
  top: 36vw;
  font-size: 5vw;
}
.mask .popup2 .qr_area {
  position: absolute;
  top: 52vw;
}
.mask .popup2 .qr_area .qr {
  width: 27vw;
}
.mask .popup2 .p2 {
  color: #5E4538;
  position: absolute;
  top: 84vw;
  text-align: center;
}
.mask .popup2 .p3 {
  color: #5E4538;
  position: absolute;
  top: 98vw;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.mask .popup2 .p3 span {
  display: block;
  padding: 0 2vw;
  border-radius: 6vw;
  background-color: #fff;
  color: #005F9C;
}
.mask .popup2 .p3 .p3_content {
  margin-top: 2vw;
  text-align: center;
  font-size: 3vw;
}
.mask .popup3 {
  width: 100vw;
  height: 34vw;
  background: url(../img/popup3.png) no-repeat center top / 100% 100%;
}
.mask .popup3 .button9 {
  width: 51.3333vw;
  position: absolute;
  bottom: -17vw;
}
.mask .popup4 {
  margin-top: -20vw;
  width: 70.4vw;
  height: 82.267vw;
  background: url(../img/popup4.png) no-repeat center top / 100% 100%;
}
.mask .popup4 .p4 {
  margin-top: 30vw;
  color: #5e4538;
  font-weight: bold;
  font-size: 4vw;
}
.mask .popup4 .p4 span {
  color: #dc2a18;
  font-size: 5vw;
}
.mask .popup4 .button10 {
  width: 36.5333vw;
  position: absolute;
  bottom: 4vw;
}
.mask .popup5 {
  width: 65.2vw;
  height: 62vw;
  background: url(../img/popup5.png) no-repeat center top / 100% 100%;
}
.mask .popup5 .button11 {
  width: 36.5333vw;
  position: absolute;
  bottom: 4vw;
}
.mask .popup6 {
  width: 81.467vw;
  height: 90.267vw;
  background: rgba(255, 255, 255, 0.42);
  border-radius: 2vw;
  border: 1px solid #000000;
}
.mask .popup6 .select_list_area {
  width: 76vw;
  height: 84.267vw;
  background: rgba(66, 133, 199, 0.9);
  border-radius: 2vw;
  border: 1px solid #000000;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.mask .popup6 .select_list_area .popup6_tit {
  margin-top: 4vw;
  width: 33.2vw;
}
.mask .popup6 .select_list_area .list {
  display: flex;
  flex-direction: column;
  width: 100%;
}
.mask .popup6 .select_list_area .list .item {
  margin-top: 6vw;
  width: 100%;
  padding: 0 6.6667vw;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.mask .popup6 .select_list_area .list .item .close3 {
  width: 5vw;
  height: 5vw;
  background: url(../img/close.png) no-repeat center center / 100% 100%;
}
.mask .popup8 {
  width: 69.6vw;
  height: 19.067vw;
  background: rgba(255, 255, 255, 0.79);
  border-radius: 2.267vw;
  border: 1px solid #000100;
  font-size: 5.467vw;
  color: #906100;
  line-height: 7.6vw;
  text-align: center;
  font-weight: bold;
}
.mask .poster {
  width: 100vw;
  animation: sc 0.5s ease-in-out forwards;
  animation-delay: 0.5s;
}
.mask .white {
  width: 100vw;
  height: 100vh;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 3;
  background-color: white;
  /* 设置为你想要的背景颜色 */
  animation: flash 0.5s ease-in-out forwards;
  /* 定义动画名称、时长和缓动函数 */
  pointer-events: none;
}
.mask .poster_tip {
  color: #fff;
  position: absolute;
  bottom: 27vw;
  font-weight: bold;
  letter-spacing: 0.4vw;
  animation: flash2 1s ease-in-out forwards;
}
.mask .close {
  width: 8vw;
  height: 8vw;
  background: url(../img/close.png) no-repeat center center / 100% 100%;
  position: absolute;
  bottom: 10vw;
  animation: flash2 1s ease-in-out forwards;
}
.mask .close2 {
  width: 10vw;
  height: 10vw;
  background: url(../img/close.png) no-repeat center center / 100% 100%;
  position: absolute;
  bottom: -15vw;
}
.blink-2 {
  animation: blink-2 1s linear infinite both;
}
@keyframes blink-2 {
  0%,
  10%,
  90%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.2;
  }
}
@keyframes flash {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
@keyframes flash2 {
  0%,
  99% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@keyframes sc {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(0.7) translateY(-10vw);
  }
}
@keyframes pulsate-bck2 {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}
